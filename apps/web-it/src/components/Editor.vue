<script setup>
import { computed } from 'vue';

import Editor from '@tinymce/tinymce-vue';

// 导入 TinyMCE 核心
import 'tinymce/tinymce';
import 'tinymce/themes/silver';
import 'tinymce/icons/default';
import 'tinymce/models/dom';
import 'tinymce/plugins/code';
import 'tinymce/plugins/lists';
import 'tinymce/plugins/link';
import 'tinymce/plugins/image';
import 'tinymce/plugins/table';
import 'tinymce/plugins/wordcount';
import 'tinymce/plugins/help';

const props = defineProps({
  config: {
    type: Object,
    default: () => ({}),
  },
});
const editorContent = defineModel({ type: String });
// TinyMCE 初始化配置
const editorConfig = computed(() => {
  return {
    height: 500,
    promotion: false,
    language: 'zh_CN',
    branding: false,
    base_url: '/tinymce',
    skin_url: '/tinymce/skins/ui/oxide',
    content_css: '/tinymce/skins/content/default/content.css',
    language_url: '/tinymce_lang/zh_CN.js',
    ...props.config,
  };
});
</script>

<template>
  <div>
    <Editor v-model="editorContent" :init="editorConfig" license-key="gpl" />
  </div>
</template>

<style></style>
