<script setup lang="ts">
import type { NoticeInfo } from '#/api';

import { computed, reactive, ref } from 'vue';

import { BASE_PAGE_CLASS_NAME, FORM_PROP } from '@vben/constants';
import { BasicPopup, usePopupInner } from '@vben/fe-ui';

import Editor from '#/components/Editor.vue';
import dayjs from 'dayjs';

import { BaseFeUserSelect } from '#/adapter/fe-ui';
import { useDictStore } from '#/store/dict';

const emit = defineEmits(['register', 'ok']);
const dictStore = useDictStore();
const state = reactive({
  loading: false,
  formProp: FORM_PROP,
});
const noticeForm = ref<NoticeInfo>({});
const publishTime = computed({
  get() {
    return dayjs(noticeForm.value.publishTime).format('x');
  },
  set(value) {
    noticeForm.value.publishTime = Number(value);
  },
});
const init = async (data: NoticeInfo) => {
  noticeForm.value = data;
};
const title = computed(() => {
  return noticeForm.value.id ? '编辑公告' : '新增公告';
});
const [registerPopup] = usePopupInner(init);
</script>

<template>
  <BasicPopup v-bind="$attrs" :title="title" @register="registerPopup">
    <div :class="BASE_PAGE_CLASS_NAME" class="mt-4">
      <a-form :model="noticeForm" v-bind="state.formProp">
        <a-row>
          <a-col :span="12">
            <a-form-item label="标题" name="title">
              <a-input v-model:value="noticeForm.title" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="分类" name="noticeType">
              <a-select v-model:value="noticeForm.noticeType" :options="dictStore.getDictList('NOTICE_TYPE')" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="通知范围" name="noticeScope">
              <a-radio-group v-model:value="noticeForm.noticeScope" :options="dictStore.getDictList('NOTICE_SCOPE')" />
            </a-form-item>
          </a-col>
          <a-col v-if="noticeForm.noticeScope?.includes('2')" :span="24">
            <a-form-item label="通知用户" name="noticeUsers">
              <BaseFeUserSelect v-model:value="noticeForm.noticeUsers" multiple />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="通知方式" name="noticeMethods">
              <a-checkbox-group
                v-model:value="noticeForm.noticeMethods"
                :options="dictStore.getDictList('NOTICE_METHOD')"
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="定时发布" name="isTiming">
              <a-switch v-model:checked="noticeForm.isTiming" :checked-value="1" :un-checked-value="0" />
            </a-form-item>
          </a-col>
          <a-col v-if="noticeForm.isTiming" :span="12">
            <a-form-item label="发布时间" name="publishTime">
              <a-date-picker v-model:value="publishTime" value-format="x" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="置顶" name="isTop">
              <a-switch v-model:checked="noticeForm.isTop" :checked-value="1" :un-checked-value="0" />
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <Editor v-model="noticeForm.content" />
          </a-col>
        </a-row>
      </a-form>
    </div>
  </BasicPopup>
</template>

<style></style>
