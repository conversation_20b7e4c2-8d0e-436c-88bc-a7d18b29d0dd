import { defineConfig } from '@vben/vite-config';

import { viteStaticCopy } from 'vite-plugin-static-copy';

export default defineConfig(async () => {
  return {
    application: {},
    vite: {
      // plugins: [
      //   viteStaticCopy({
      //     targets: [
      //       {
      //         src: '../../node_modules/.pnpm/tinymce@8.0.2/node_modules/tinymce/**/*',
      //         dest: 'tinymce',
      //       },
      //     ],
      //   }),
      // ],
      server: {
        proxy: {
          '/jxct/api': {
            changeOrigin: true,
            // mock代理目标地址
            target: 'http://*************',
            ws: true,
          },
          // '/jxct/api': {
          //   changeOrigin: true,
          //   rewrite: (path) => path.replace(/^\/jxct\/api/, ''),
          //   // mock代理目标地址
          //   target: 'http://localhost:5320/api',
          //   ws: true,
          // },
        },
      },
    },
  };
});
