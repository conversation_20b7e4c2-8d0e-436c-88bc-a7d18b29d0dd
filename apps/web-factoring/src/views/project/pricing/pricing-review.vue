<script setup lang="ts">
import type { PricingInfo } from '#/api';

import { reactive, ref } from 'vue';

import {
  BASE_PAGE_CLASS_NAME,
  COL_SPAN_PROP,
  DESCRIPTIONS_PROP,
  FORM_PROP,
  FULL_FORM_ITEM_PROP,
} from '@vben/constants';
import { BasicCaption, BasicPopup, usePopupInner } from '@vben/fe-ui';
import { useDictStore } from '@vben/stores';

import { message } from 'ant-design-vue';
import dayjs from 'dayjs';
import { cloneDeep, omit } from 'lodash-es';

import { BaseAttachmentList } from '#/adapter/base-ui';
import {
  getPricingInfoApi,
  recheckComprehensivePricingApi,
  recheckSinglePricingApi,
  rejectRecheckPricingApi,
} from '#/api';
import DebtServiceDetail from '#/views/project/components/debt-service-detail.vue';
import RepaymentCalculationDetail from '#/views/project/components/repayment-calculation-detail.vue';
import { validateCreditRate } from '#/views/project/components/validationCreditRate.ts';
import BaseDetail from '#/views/project/pricing/components/base-detail.vue';

const emit = defineEmits(['ok', 'register']);

const dictStore = useDictStore();
const init = async (data: PricingInfo) => {
  let info = data.id ? await getPricingInfoApi(data.id as number) : data;
  if (info.projectType === 'single') {
    const calculation = omit(
      info.calculation,
      'id',
      'targetCompanyName',
      'targetCompanyCode',
      'projectCode',
      'projectName',
    );
    info = {
      ...info,
      ...calculation,
    };
  }
  info.expectedLaunchDate = dayjs(info.expectedLaunchDate).valueOf().toString();
  info.expectedDueDate = dayjs(info.expectedDueDate).valueOf().toString();
  pricingForm.value = info;
};
const [registerPopup, { closePopup }] = usePopupInner(init);
const FormRef = ref();
const pricingForm = ref<PricingInfo>({});
const loading = reactive({
  submit: false,
});
const save = async (type?: string) => {
  await FormRef.value.validate();
  // 授信费率>=定价底线（基准定价+浮动定价）
  const result = validateCreditRate(pricingForm.value);

  if (!result.passed) {
    message.error(result.message);
    return;
  }
  let api;
  if (pricingForm.value.id) {
    api = pricingForm.value.projectType === 'comprehensive' ? recheckComprehensivePricingApi : recheckSinglePricingApi;
  }
  const formData = cloneDeep(pricingForm.value);
  loading.submit = true;
  if (type === 'submit') formData.isSubmit = true;
  try {
    const res = await api(formData);
    message.success('保存成功');
    closePopup();
    emit('ok', res);
  } finally {
    loading.submit = false;
  }
};
const reject = async () => {
  loading.submit = true;
  try {
    const res = await rejectRecheckPricingApi(pricingForm.value.id as number);
    message.success('驳回成功');
    closePopup();
    emit('ok', res);
  } finally {
    loading.submit = false;
  }
};
const rules = {
  pricingFloatingRatio: [{ required: true, message: '请输入浮动定价' }],
  comprehensiveRate: [{ required: true, message: '请输入综合收益率' }],
  pricingDesc: [{ required: true, message: '请输入定价方案说明' }],
};
const formProp = { ...FORM_PROP, labelCol: { span: 6 }, wrapperCol: { span: 18 } };
const colSpan = COL_SPAN_PROP;
const fullProp = { ...FULL_FORM_ITEM_PROP, labelCol: { span: 3 }, wrapperCol: { span: 21 } };
const descriptionsProp = {
  ...DESCRIPTIONS_PROP,
  labelStyle: {
    width: '220px',
    justifyContent: 'flex-end',
  },
};
</script>

<template>
  <BasicPopup v-bind="$attrs" title="项目定价信息" @register="registerPopup">
    <template #insertToolbar>
      <a-space>
        <a-button type="primary" :loading="loading.submit" danger @click="reject">驳回</a-button>
        <a-button type="primary" :loading="loading.submit" @click="save('save')">保存</a-button>
        <a-button type="primary" :loading="loading.submit" @click="save('submit')">提交</a-button>
      </a-space>
    </template>
    <div :class="BASE_PAGE_CLASS_NAME">
      <BaseDetail :pricing-form="pricingForm" :descriptions-prop="descriptionsProp" />
      <BasicCaption :content="`一般定价方案（${dictStore.formatter(pricingForm.projectType, 'FCT_PROJECT_TYPE')}）`" />
      <a-descriptions v-bind="descriptionsProp" class="mt-4">
        <template v-if="pricingForm.projectType === 'comprehensive'">
          <a-descriptions-item label="授信额度（元）">
            {{ pricingForm.pricingCreditAmount }}
          </a-descriptions-item>
          <a-descriptions-item label="授信期限（个月）">
            {{ pricingForm.pricingCreditTerm }}
          </a-descriptions-item>
          <a-descriptions-item label="授信费率（%）">
            {{ pricingForm.pricingCreditRate }}
          </a-descriptions-item>
          <a-descriptions-item label="授信额度类型">
            {{ dictStore.formatter(pricingForm.pricingCreditType, 'FCT_CREDIT_TYPE') }}
          </a-descriptions-item>
        </template>
        <a-descriptions-item label="基准定价（%/年）">
          {{ pricingForm.pricingBasicRatio }}
        </a-descriptions-item>
        <template v-if="pricingForm.projectType === 'single'">
          <a-descriptions-item label="综合收益率（%/年）">
            {{ pricingForm.pricingXirrRate }}
          </a-descriptions-item>
        </template>
      </a-descriptions>
      <a-form ref="FormRef" class="mt-5" :model="pricingForm" :rules="rules" v-bind="formProp">
        <a-row class="mt-5">
          <a-col v-bind="colSpan">
            <a-form-item label="浮动定价（%/年）" name="pricingFloatingRatio">
              <a-input v-model:value="pricingForm.pricingFloatingRatio" />
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="定价方案说明" name="pricingDesc" v-bind="fullProp">
              <a-textarea v-model:value="pricingForm.pricingDesc" :rows="4" class="w-full" />
            </a-form-item>
          </a-col>
        </a-row>
      </a-form>
      <div v-show="pricingForm.projectType === 'single'">
        <DebtServiceDetail :debt-service-form="pricingForm" :descriptions-prop="descriptionsProp" />
        <RepaymentCalculationDetail
          :calculation-form="pricingForm"
          :descriptions-prop="descriptionsProp"
          calculation-type="Pricing"
        />
      </div>
      <BaseAttachmentList
        v-model="pricingForm.attachmentList"
        :business-id="pricingForm.id"
        business-type="FCT_PROJECT_PRICING"
        edit-mode
      />
    </div>
  </BasicPopup>
</template>

<style></style>
