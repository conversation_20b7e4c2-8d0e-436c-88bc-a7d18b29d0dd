<script setup lang="ts">
import type { PricingInfo } from '#/api';

import { nextTick, reactive, ref } from 'vue';

import {
  BASE_PAGE_CLASS_NAME,
  COL_SPAN_PROP,
  DESCRIPTIONS_PROP,
  FORM_PROP,
  FULL_FORM_ITEM_PROP,
} from '@vben/constants';
import { BasicCaption, BasicPopup, usePopupInner } from '@vben/fe-ui';
import { useDictStore } from '@vben/stores';

import { message } from 'ant-design-vue';
import dayjs from 'dayjs';
import { cloneDeep, omit } from 'lodash-es';

import { BaseAttachmentList } from '#/adapter/base-ui';
import { changeComprehensivePricingApi, changeSinglePricingApi, getOverviewInfoApi, getPricingInfoApi } from '#/api';
import DebtServiceDetail from '#/views/project/components/debt-service-detail.vue';
import RepaymentCalculationHistory from '#/views/project/components/repayment-calculation-history.vue';
import RepaymentCalculation from '#/views/project/components/repayment-calculation.vue';
import { validateCreditRate } from '#/views/project/components/validationCreditRate.ts';
import BaseDetail from '#/views/project/pricing/components/base-detail.vue';

const emit = defineEmits(['ok', 'register']);
const dictStore = useDictStore();
const init = async (data: PricingInfo) => {
  let info = data.id ? await getPricingInfoApi(data.id as number) : data;
  if (info.projectType === 'single') {
    const calculation = omit(
      info.calculation,
      'id',
      'targetCompanyName',
      'targetCompanyCode',
      'projectCode',
      'projectName',
    );
    info = {
      ...info,
      ...calculation,
    };
  } else {
    const res = await getOverviewInfoApi(info.projectId);
    const {
      creditAmount: pricingCreditAmount,
      creditTerm: pricingCreditTerm,
      creditRate: pricingCreditRate,
      creditType: pricingCreditType,
    } = res;
    info = {
      ...info,
      pricingCreditAmount,
      pricingCreditTerm,
      pricingCreditRate,
      pricingCreditType,
    };
  }
  info.expectedLaunchDate = dayjs(info.expectedLaunchDate).valueOf().toString();
  info.expectedDueDate = dayjs(info.expectedDueDate).valueOf().toString();
  pricingForm.value = info;
  nextTick(() => {
    if (pricingForm.value.projectType === 'single') {
      RepaymentCalculationRef.value.init(pricingForm.value);
      RepaymentCalculationHistoryRef.value.init(pricingForm.value);
    }
  });
};
const [registerPopup, { closePopup }] = usePopupInner(init);
const FormRef = ref();
const RepaymentCalculationRef = ref();
const RepaymentCalculationHistoryRef = ref();
const pricingForm = ref<PricingInfo>({});
const loading = reactive({
  submit: false,
});
const save = async (type?: string) => {
  await FormRef.value.validate();
  // 授信费率>=定价底线（基准定价+浮动定价）
  const result = validateCreditRate(pricingForm.value);

  if (!result.passed) {
    message.error(result.message);
    return;
  }
  if (pricingForm.value.projectType === 'single') {
    const repaymentResult = await RepaymentCalculationRef.value.save();
    if (!repaymentResult) {
      return;
    }
  }
  let api;
  if (pricingForm.value.id) {
    api = pricingForm.value.projectType === 'comprehensive' ? changeComprehensivePricingApi : changeSinglePricingApi;
  }
  const formData = cloneDeep(pricingForm.value);
  formData.expectedLaunchDate = Number(formData.expectedLaunchDate);
  formData.expectedDueDate = Number(formData.expectedDueDate);
  formData.calculation = { ...formData };
  delete formData.detailList;
  loading.submit = true;
  if (type === 'submit') formData.isSubmit = true;
  try {
    const res = await api(formData);
    message.success('保存成功');
    closePopup();
    emit('ok', res);
  } finally {
    loading.submit = false;
  }
};
const rules = {
  pricingCreditRate: [{ required: true, message: '请输入授信费率' }],
  pricingBasicRatio: [{ required: true, message: '请输入基准定价' }],
  pricingFloatingRatio: [{ required: true, message: '请输入浮动定价' }],
  comprehensiveRate: [{ required: true, message: '请输入综合收益率' }],
  pricingDesc: [{ required: true, message: '请输入定价方案说明' }],
  planningMethod: [{ required: true, message: '请选择还本付息计划规划方式', trigger: 'change' }],
  expectedUseAmount: [{ required: true, message: '请输入计划融资金额' }],
  nominalInterestRate: [{ required: true, message: '请输入合同利率' }],
  expectedLaunchDate: [{ required: true, message: '请选择预计业务投放日', trigger: 'change' }],
  expectedDueDate: [{ required: true, message: '请选择预估最后还款日', trigger: 'change' }],
  principalRepaymentMethod: [{ required: true, message: '请选择还本方式', trigger: 'change' }],
  interestRepaymentMethod: [{ required: true, message: '请选择还息方式', trigger: 'change' }],
  principalPeriod: [{ required: true, message: '请选择分期还本频次', trigger: 'change' }],
  interestPeriod: [{ required: true, message: '请选择分期还息频次', trigger: 'change' }],
  repayPrincipalDay: [{ required: true, message: '请选择默认当期还本日', trigger: 'change' }],
  repayInterestDay: [{ required: true, message: '请选择默认当期还息日', trigger: 'change' }],
};
const formProp = { ...FORM_PROP, labelCol: { span: 6 }, wrapperCol: { span: 18 } };
const colSpan = COL_SPAN_PROP;
const fullProp = { ...FULL_FORM_ITEM_PROP, labelCol: { span: 3 }, wrapperCol: { span: 21 } };
const descriptionsProp = {
  ...DESCRIPTIONS_PROP,
  labelStyle: {
    width: '220px',
    justifyContent: 'flex-end',
  },
};
</script>

<template>
  <BasicPopup v-bind="$attrs" title="项目定价信息" @register="registerPopup">
    <template #insertToolbar>
      <a-space>
        <a-button type="primary" :loading="loading.submit" @click="save">保存</a-button>
        <a-button type="primary" :loading="loading.submit" @click="save('submit')">提交</a-button>
      </a-space>
    </template>
    <div :class="BASE_PAGE_CLASS_NAME">
      <BaseDetail :pricing-form="pricingForm" :descriptions-prop="descriptionsProp" />
      <BasicCaption :content="`一般定价方案（${dictStore.formatter(pricingForm.projectType, 'FCT_PROJECT_TYPE')}）`" />
      <a-form ref="FormRef" class="mt-5" :model="pricingForm" :rules="rules" v-bind="formProp">
        <a-row class="mt-5">
          <template v-if="pricingForm.projectType === 'comprehensive'">
            <a-col v-bind="colSpan">
              <a-form-item label="授信金额（元）" name="pricingCreditAmount">
                <a-input v-model:value="pricingForm.pricingCreditAmount" disabled />
              </a-form-item>
            </a-col>
            <a-col v-bind="colSpan">
              <a-form-item label="授信期限（个月）" name="pricingCreditTerm">
                <a-input v-model:value="pricingForm.pricingCreditTerm" disabled />
              </a-form-item>
            </a-col>
            <a-col v-bind="colSpan">
              <a-form-item label="授信费率（%）" name="pricingCreditRate">
                <a-input v-model:value="pricingForm.pricingCreditRate" />
              </a-form-item>
            </a-col>
            <a-col v-bind="colSpan">
              <a-form-item label="授信额度类型">
                {{ dictStore.formatter(pricingForm.pricingCreditType, 'FCT_CREDIT_TYPE') }}
              </a-form-item>
            </a-col>
          </template>
          <template v-if="pricingForm.projectType">
            <a-col v-bind="colSpan">
              <a-form-item label="基准定价（%/年）" name="pricingBasicRatio">
                <a-input v-model:value="pricingForm.pricingBasicRatio" />
              </a-form-item>
            </a-col>
          </template>
          <template v-if="pricingForm.projectType === 'single'">
            <a-col v-bind="colSpan">
              <a-form-item label="综合收益率（%/年）" name="pricingXirrRate">
                <a-input v-model:value="pricingForm.pricingXirrRate" />
              </a-form-item>
            </a-col>
          </template>
          <a-col v-bind="colSpan">
            <a-form-item label="浮动定价（%/年）" name="pricingFloatingRatio">
              <a-input v-model:value="pricingForm.pricingFloatingRatio" />
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="定价方案说明" name="pricingDesc" v-bind="fullProp">
              <a-textarea v-model:value="pricingForm.pricingDesc" :rows="4" class="w-full" />
            </a-form-item>
          </a-col>
        </a-row>
        <div v-if="pricingForm.projectType === 'single'">
          <DebtServiceDetail :debt-service-form="pricingForm" :descriptions-prop="descriptionsProp" />
          <RepaymentCalculation ref="RepaymentCalculationRef" v-model="pricingForm" calculation-type="Pricing" />
          <RepaymentCalculationHistory ref="RepaymentCalculationHistoryRef" calculation-type="Pricing" />
        </div>
      </a-form>
      <BaseAttachmentList
        v-model="pricingForm.attachmentList"
        :business-id="pricingForm.id"
        business-type="FCT_PROJECT_PRICING"
        edit-mode
      />
    </div>
  </BasicPopup>
</template>

<style></style>
