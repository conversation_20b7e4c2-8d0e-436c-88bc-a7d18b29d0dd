<script setup lang="ts">
import type { CreditPricingInfo } from '#/api';

import { reactive, ref } from 'vue';

import {
  BASE_PAGE_CLASS_NAME,
  COL_SPAN_PROP,
  DESCRIPTIONS_PROP,
  FORM_PROP,
  FULL_FORM_ITEM_PROP,
} from '@vben/constants';
import { BasicCaption, BasicPopup, usePopupInner } from '@vben/fe-ui';

import { message } from 'ant-design-vue';
import dayjs from 'dayjs';
import { cloneDeep } from 'lodash-es';

import { BaseAttachmentList } from '#/adapter/base-ui';
import { changeCreditPricingApi, getCreditPricingInfoApi, getProjectLimitApi } from '#/api';
import DebtServiceDetail from '#/views/project/components/debt-service-detail.vue';
import RepaymentCalculationHistory from '#/views/project/components/repayment-calculation-history.vue';
import RepaymentCalculation from '#/views/project/components/repayment-calculation.vue';
import { validateCreditRate } from '#/views/project/components/validationCreditRate.ts';
import BaseDetail from '#/views/project/credit-pricing/components/base-detail.vue';

const emit = defineEmits(['ok', 'register']);

const init = async (data: CreditPricingInfo) => {
  let info = data.id ? await getCreditPricingInfoApi(data.id as number) : data;
  const res = await getProjectLimitApi({ projectId: data.projectId });
  const {
    creditAmount: pricingCreditAmount,
    creditTerm: pricingCreditTerm,
    creditRate: pricingCreditRate,
    creditType: pricingCreditType,
  } = res;
  const { id: _, ...calculation } = info.calculation;
  info = {
    ...info,
    ...calculation,
    pricingCreditAmount,
    pricingCreditTerm,
    pricingCreditRate,
    pricingCreditType,
  };
  info.expectedLaunchDate = dayjs(info.expectedLaunchDate).valueOf().toString();
  info.expectedDueDate = dayjs(info.expectedDueDate).valueOf().toString();
  creditPricingForm.value = info;
  RepaymentCalculationRef.value.init(creditPricingForm.value);
  RepaymentCalculationHistoryRef.value.init(creditPricingForm.value);
};
const [registerPopup, { closePopup }] = usePopupInner(init);
const FormRef = ref();
const RepaymentCalculationRef = ref();
const RepaymentCalculationHistoryRef = ref();
const creditPricingForm = ref<CreditPricingInfo>({});
const loading = reactive({
  submit: false,
});
const save = async (type?: string) => {
  await FormRef.value.validate();

  // 授信费率>=定价底线（基准定价+浮动定价）
  const result = validateCreditRate(creditPricingForm.value);

  if (!result.passed) {
    message.error(result.message);
    return;
  }
  const repaymentResult = await RepaymentCalculationRef.value.save();
  if (!repaymentResult) {
    return;
  }
  let api;
  if (creditPricingForm.value.id) {
    api = changeCreditPricingApi;
  }
  const formData = cloneDeep(creditPricingForm.value);
  formData.expectedLaunchDate = Number(formData.expectedLaunchDate);
  formData.expectedDueDate = Number(formData.expectedDueDate);
  formData.calculation = { ...formData };
  delete formData.detailList;
  loading.submit = true;
  if (type === 'submit') formData.isSubmit = true;
  try {
    const res = await api(formData);
    message.success('保存成功');
    closePopup();
    emit('ok', res);
  } finally {
    loading.submit = false;
  }
};
const rules = {
  pricingFloatingRatio: [{ required: true, message: '请输入浮动定价' }],
  pricingDesc: [{ required: true, message: '请输入定价方案说明' }],
  pricingBasicRatio: [{ required: true, message: '请输入基准利率' }],
  pricingXirrRate: [{ required: true, message: '请输入综合收益率' }],
  planningMethod: [{ required: true, message: '请选择还本付息计划规划方式', trigger: 'change' }],
  expectedUseAmount: [{ required: true, message: '请输入拟用信金额' }],
  nominalInterestRate: [{ required: true, message: '请输入合同利率' }],
  expectedLaunchDate: [{ required: true, message: '请选择预计业务投放日', trigger: 'change' }],
  expectedDueDate: [{ required: true, message: '请选择预估最后还款日', trigger: 'change' }],
  principalRepaymentMethod: [{ required: true, message: '请选择还本方式', trigger: 'change' }],
  interestRepaymentMethod: [{ required: true, message: '请选择还息方式', trigger: 'change' }],
  principalPeriod: [{ required: true, message: '请选择分期还本频次', trigger: 'change' }],
  interestPeriod: [{ required: true, message: '请选择分期还息频次', trigger: 'change' }],
  repayPrincipalDay: [{ required: true, message: '请选择默认当期还本日', trigger: 'change' }],
  repayInterestDay: [{ required: true, message: '请选择默认当期还息日', trigger: 'change' }],
};
const formProp = { ...FORM_PROP, labelCol: { span: 8 }, wrapperCol: { span: 16 } };
const colSpan = COL_SPAN_PROP;
const fullProp = { ...FULL_FORM_ITEM_PROP, labelCol: { span: 4 }, wrapperCol: { span: 20 } };
const descriptionsProp = {
  ...DESCRIPTIONS_PROP,
  labelStyle: {
    width: '220px',
    justifyContent: 'flex-end',
  },
};
</script>

<template>
  <BasicPopup title="用信定价" @register="registerPopup">
    <template #insertToolbar>
      <a-space>
        <a-button type="primary" :loading="loading.submit" @click="save">保存</a-button>
        <a-button type="primary" :loading="loading.submit" @click="save('submit')">提交</a-button>
      </a-space>
    </template>
    <div :class="BASE_PAGE_CLASS_NAME">
      <BaseDetail :credit-pricing-form="creditPricingForm" :descriptions-prop="descriptionsProp" />
      <a-form ref="FormRef" class="mt-5" :model="creditPricingForm" :rules="rules" v-bind="formProp">
        <BasicCaption content="用信定价方案" />
        <a-row class="mt-5">
          <a-col v-bind="colSpan">
            <a-form-item label="用信主体">
              {{ creditPricingForm.creditUseCompanyName }}
            </a-form-item>
          </a-col>
          <a-col v-bind="colSpan">
            <a-form-item label="基准利率（%/年）" name="pricingBasicRatio">
              <a-input v-model:value="creditPricingForm.pricingBasicRatio" />
            </a-form-item>
          </a-col>
          <a-col v-bind="colSpan">
            <a-form-item label="用信定价综合收益率(%/年)" name="pricingXirrRate">
              <a-input v-model:value="creditPricingForm.pricingXirrRate" />
            </a-form-item>
          </a-col>
          <a-col v-bind="colSpan">
            <a-form-item label="浮动利率（%/年）" name="pricingFloatingRatio">
              <a-input v-model:value="creditPricingForm.pricingFloatingRatio" />
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="定价方案说明" name="pricingDesc" v-bind="fullProp">
              <a-textarea v-model:value="creditPricingForm.pricingDesc" :rows="4" class="w-full" />
            </a-form-item>
          </a-col>
        </a-row>
        <DebtServiceDetail :debt-service-form="creditPricingForm" :descriptions-prop="descriptionsProp" />
        <RepaymentCalculation
          ref="RepaymentCalculationRef"
          v-model="creditPricingForm"
          calculation-type="CreditPricing"
        />
        <RepaymentCalculationHistory ref="RepaymentCalculationHistoryRef" calculation-type="CreditPricing" />
        <BaseAttachmentList
          v-model="creditPricingForm.attachmentList"
          :business-id="creditPricingForm.id"
          business-type="FCT_PROJECT_CREDIT_PRICING"
          edit-mode
        />
      </a-form>
    </div>
  </BasicPopup>
</template>

<style></style>
