# SimpleUpload 修复说明

## 问题原因

之前的高阶组件实现有属性合并顺序问题，导致 v-model 绑定失效。

## 修复方案

使用更简单直接的组件包装方式：

```typescript
export const SimpleUpload = defineComponent({
  name: 'SimpleUpload',
  inheritAttrs: false,
  setup(_, { attrs, slots }) {
    // 关键：默认值在前，传入属性在后
    const finalProps = {
      uploadApi: uploadFileApi,        // 默认值
      previewApi: getDownloadFileLinkApi, // 默认值
      ...attrs, // 传入的属性会覆盖默认值，包括 v-model 绑定
    };

    return () => h(BaseUpload, finalProps, slots);
  },
});
```

## 关键点

1. **属性合并顺序**：`...attrs` 在后面，确保传入的属性优先级更高
2. **`attrs` 包含所有属性**：包括 v-model 绑定、事件监听器等
3. **`inheritAttrs: false`**：完全控制属性传递

## 测试验证

现在以下用法都应该正常工作：

```vue
<!-- v-model:link 绑定 -->
<SimpleUpload v-model:link="imageUrl" accept="image/*" />

<!-- v-model:id-list 绑定 -->
<SimpleUpload v-model:id-list="fileIdList" :max-count="3" />

<!-- v-model 绑定 -->
<SimpleUpload v-model="fileId" />

<!-- 覆盖默认 API -->
<SimpleUpload 
  v-model:link="customUrl"
  :upload-api="customUploadApi"
  :preview-api="customPreviewApi"
/>
```

## 使用方式

```vue
<script setup>
import { SimpleUpload } from '#/components';

const imageUrl = ref('');
</script>

<template>
  <SimpleUpload v-model:link="imageUrl" accept="image/*" />
</template>
```

现在 `v-model:link="configForm.imgLogo"` 应该可以正常工作了！
